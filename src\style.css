/* 现代化中文界面样式 */
:root {
  --primary-color: #3b82f6;
  --secondary-color: #6366f1;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-color: #0f172a;
  --surface-color: #1e293b;
  --card-color: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;
  --border-color: #475569;
  --accent-color: #00d4ff;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', '微软雅黑', sans-serif;
  background: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  overflow: hidden;
}

/* 加载画面 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--background-color);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 主应用布局 */
.signal-journey-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
}

/* 头部样式 */
.app-header {
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 2rem;
  box-shadow: var(--shadow-md);
  z-index: 100;
}

.app-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.app-subtitle {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

/* 模式选择器 */
.mode-selector {
  display: flex;
  gap: 1rem;
}

.mode-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--card-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  font-size: 0.9rem;
}

.mode-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.mode-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.mode-icon {
  font-size: 1.2rem;
}

/* 主内容区 */
.app-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.scene-container {
  flex: 1;
  background: #000;
  position: relative;
  overflow: hidden;
}

.control-panel {
  width: 400px;
  background: var(--surface-color);
  border-left: 1px solid var(--border-color);
  overflow-y: auto;
  padding: 1.5rem;
}

/* 控制组件样式 */
.control-section {
  margin-bottom: 2rem;
  background: var(--card-color);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.control-section h3 {
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
}

.control-group {
  margin-bottom: 1.5rem;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-group label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* 输入元素样式 */
.bit-input, .select-input {
  width: 100%;
  padding: 0.75rem;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.bit-input:focus, .select-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-row {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.input-row .bit-input {
  flex: 1;
}

/* 滑块样式 */
.slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--border-color);
  outline: none;
  -webkit-appearance: none;
  transition: all 0.3s ease;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: var(--secondary-color);
  transform: scale(1.1);
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-md);
}

/* 按钮样式 */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--card-color);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--border-color);
  color: var(--text-primary);
}

.btn-info {
  background: var(--accent-color);
  color: var(--background-color);
}

.btn-info:hover:not(:disabled) {
  background: #00b8d4;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.button-row {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* 复选框样式 */
.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  margin-bottom: 0.75rem;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 帮助文本 */
.help-text {
  color: var(--text-muted);
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* 信息面板 */
.info-panel, .medium-info-panel, .performance-panel {
  background: var(--background-color);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  border: 1px solid var(--border-color);
}

.info-panel h4, .medium-info-panel h4, .performance-panel h4 {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-grid, .performance-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.info-item, .performance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.info-item:last-child, .performance-item:last-child {
  border-bottom: none;
}

.info-label, .performance-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.info-value, .performance-value {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

.cost-indicator, .complexity-indicator {
  color: var(--warning-color);
  font-size: 1rem;
}

.medium-description {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.medium-description p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 比较表格 */
.comparison-table {
  margin-top: 1rem;
  background: var(--card-color);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.comparison-table table {
  width: 100%;
  border-collapse: collapse;
}

.comparison-table th {
  background: var(--background-color);
  color: var(--text-primary);
  padding: 0.75rem;
  text-align: left;
  font-size: 0.9rem;
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
}

.comparison-table td {
  padding: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
  border-bottom: 1px solid var(--border-color);
}

.comparison-table tr:last-child td {
  border-bottom: none;
}

.comparison-table tr.selected-medium {
  background: rgba(59, 130, 246, 0.1);
}

.comparison-table tr.selected-medium td {
  color: var(--text-primary);
}

/* 统计面板 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-item {
  background: var(--background-color);
  padding: 1rem;
  border-radius: 6px;
  text-align: center;
  border: 1px solid var(--border-color);
}

.stat-label {
  display: block;
  color: var(--text-muted);
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
}

.stat-value {
  display: block;
  color: var(--text-primary);
  font-size: 1.2rem;
  font-weight: 600;
}

/* 参数面板 */
.parameter-panel {
  background: var(--background-color);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  border: 1px solid var(--border-color);
}

.parameter-panel h4 {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.info-text {
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 6px;
  border-left: 3px solid var(--primary-color);
}

.info-text p {
  color: var(--text-secondary);
  font-size: 0.85rem;
  line-height: 1.4;
}

/* QAM 星座图 */
.qam-constellation {
  margin-bottom: 1rem;
}

.qam-constellation label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.qam-constellation canvas {
  width: 100%;
  max-width: 200px;
  height: 200px;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

.qam-controls {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

/* 底部栏 */
.app-footer {
  background: var(--surface-color);
  border-top: 1px solid var(--border-color);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.footer-info {
  display: flex;
  gap: 2rem;
  color: var(--text-muted);
  font-size: 0.8rem;
}

.footer-controls {
  display: flex;
  gap: 1rem;
}

.footer-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.footer-btn:hover {
  background: var(--card-color);
  color: var(--text-primary);
}

/* 模态弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--surface-color);
  border-radius: 12px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  color: var(--text-primary);
  font-size: 1.3rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: var(--card-color);
  color: var(--text-primary);
}

.modal-body {
  padding: 1.5rem;
}

.help-section {
  margin-bottom: 2rem;
}

.help-section:last-child {
  margin-bottom: 0;
}

.help-section h3 {
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.help-section ul {
  list-style: none;
  padding-left: 0;
}

.help-section li {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
}

.help-section li::before {
  content: "•";
  color: var(--primary-color);
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* 错误消息 */
.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--error-color);
  border-radius: 6px;
  padding: 0.75rem;
  color: var(--error-color);
  font-size: 0.9rem;
  margin-top: 1rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .app-main {
    flex-direction: column;
  }
  
  .control-panel {
    width: 100%;
    height: 300px;
    border-left: none;
    border-top: 1px solid var(--border-color);
  }
  
  .scene-container {
    flex: 1;
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
  }
  
  .app-header h1 {
    font-size: 1.5rem;
  }
  
  .mode-selector {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .footer-info {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .footer-controls {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .button-row {
    flex-direction: column;
  }
  
  .control-panel {
    padding: 1rem;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.control-section {
  animation: fadeIn 0.5s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-color);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* 选中文本样式 */
::selection {
  background: rgba(59, 130, 246, 0.3);
  color: var(--text-primary);
}